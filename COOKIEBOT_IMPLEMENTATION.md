# Cookiebot Implementation Guide

This guide provides step-by-step instructions for implementing Cookiebot consent management and ensuring tracking pixels only fire after user consent.

## Overview

Cookiebot provides GDPR-compliant cookie consent management with four categories:
- **Necessary** - Always allowed (site functionality)
- **Preferences** - Functionality and personalization
- **Statistics** - Analytics and performance tracking
- **Marketing** - Advertising and tracking pixels

## Step 1: Add Cookiebot Script

Add the Cookiebot script to your HTML `<head>` section or Next.js `_document.js`:

```html
<script 
  id="Cookiebot" 
  src="https://consent.cookiebot.com/uc.js" 
  data-cbid="9bb0d01f-9665-4d89-b526-107da1eb142f" 
  type="text/javascript">
</script>
```

### Next.js Implementation (_document.js)

```jsx
import { Html, Head, Main, NextScript } from 'next/document'

export default function Document() {
  return (
    <Html>
      <Head>
        <script 
          id="Cookiebot" 
          src="https://consent.cookiebot.com/uc.js" 
          data-cbid="9bb0d01f-9665-4d89-b526-107da1eb142f" 
          type="text/javascript">
        </script>
      </Head>
      <body>
        <Main />
        <NextScript />
      </body>
    </Html>
  )
}
```

## Step 2: Implement Consent-Controlled Pixels

### Method 1: Data Attributes (Automatic Blocking)

Add `data-cookieconsent` attributes to your pixel scripts:

```html
<!-- Facebook Pixel -->
<script 
  data-cookieconsent="marketing" 
  type="text/javascript">
  !function(f,b,e,v,n,t,s)
  {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
  n.callMethod.apply(n,arguments):n.queue.push(arguments)};
  if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
  n.queue=[];t=b.createElement(e);t.async=!0;
  t.src=v;s=b.getElementsByTagName(e)[0];
  s.parentNode.insertBefore(t,s)}(window, document,'script',
  'https://connect.facebook.net/en_US/fbevents.js');
  fbq('init', 'YOUR_PIXEL_ID');
  fbq('track', 'PageView');
</script>

<!-- Google Analytics -->
<script 
  data-cookieconsent="statistics" 
  type="text/javascript">
  (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
  (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
  m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
  })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');
  ga('create', 'YOUR_TRACKING_ID', 'auto');
  ga('send', 'pageview');
</script>

<!-- Google Tag Manager -->
<script 
  data-cookieconsent="marketing" 
  type="text/javascript">
  (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
  new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
  j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
  'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
  })(window,document,'script','dataLayer','GTM-XXXXXXX');
</script>
```

### Method 2: JavaScript Consent Control

Create a consent management utility:

```javascript
// utils/cookiebot.js
export const CookiebotManager = {
  // Check if consent is given for a category
  hasConsent: (category) => {
    return window.Cookiebot?.consent?.[category] || false;
  },

  // Initialize pixels based on consent
  initializePixels: () => {
    if (CookiebotManager.hasConsent('marketing')) {
      CookiebotManager.initFacebookPixel();
      CookiebotManager.initGoogleAds();
    }
    
    if (CookiebotManager.hasConsent('statistics')) {
      CookiebotManager.initGoogleAnalytics();
    }
  },

  // Facebook Pixel initialization
  initFacebookPixel: () => {
    if (window.fbq) return; // Already initialized
    
    !function(f,b,e,v,n,t,s)
    {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
    n.callMethod.apply(n,arguments):n.queue.push(arguments)};
    if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
    n.queue=[];t=b.createElement(e);t.async=!0;
    t.src=v;s=b.getElementsByTagName(e)[0];
    s.parentNode.insertBefore(t,s)}(window, document,'script',
    'https://connect.facebook.net/en_US/fbevents.js');
    
    fbq('init', 'YOUR_PIXEL_ID');
    fbq('track', 'PageView');
  },

  // Google Analytics initialization
  initGoogleAnalytics: () => {
    if (window.ga) return; // Already initialized
    
    (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
    (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
    m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
    })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');
    
    ga('create', 'YOUR_TRACKING_ID', 'auto');
    ga('send', 'pageview');
  },

  // Google Ads initialization
  initGoogleAds: () => {
    // Add your Google Ads pixel code here
  }
};

// Listen for consent changes
window.addEventListener('CookiebotOnConsentReady', () => {
  CookiebotManager.initializePixels();
}, false);

// Listen for consent updates (when user changes preferences)
window.addEventListener('CookiebotOnConsentChanged', () => {
  CookiebotManager.initializePixels();
}, false);
```

### Method 3: Next.js React Hook

Create a consent hook for React components:

```jsx
// hooks/useCookiebot.js
import { useState, useEffect } from 'react';

export const useCookiebot = () => {
  const [consent, setConsent] = useState({
    necessary: true,
    preferences: false,
    statistics: false,
    marketing: false,
    loaded: false
  });

  useEffect(() => {
    const updateConsent = () => {
      if (window.Cookiebot) {
        setConsent({
          necessary: true, // Always true
          preferences: window.Cookiebot.consent.preferences,
          statistics: window.Cookiebot.consent.statistics,
          marketing: window.Cookiebot.consent.marketing,
          loaded: true
        });
      }
    };

    // Listen for Cookiebot events
    window.addEventListener('CookiebotOnConsentReady', updateConsent);
    window.addEventListener('CookiebotOnConsentChanged', updateConsent);

    // Check if already loaded
    if (window.Cookiebot) {
      updateConsent();
    }

    return () => {
      window.removeEventListener('CookiebotOnConsentReady', updateConsent);
      window.removeEventListener('CookiebotOnConsentChanged', updateConsent);
    };
  }, []);

  return consent;
};
```

Use the hook in components:

```jsx
// components/Analytics.jsx
import { useEffect } from 'react';
import { useCookiebot } from '../hooks/useCookiebot';

export default function Analytics() {
  const consent = useCookiebot();

  useEffect(() => {
    if (consent.loaded && consent.marketing) {
      // Initialize Facebook Pixel
      initFacebookPixel();
    }
    
    if (consent.loaded && consent.statistics) {
      // Initialize Google Analytics
      initGoogleAnalytics();
    }
  }, [consent]);

  return null; // This is a utility component
}
```

## Step 3: Cookie Declaration Page

Create a privacy/cookies page with the cookie declaration:

```jsx
// pages/privacy.js or pages/cookies.js
import Head from 'next/head';

export default function CookiesPage() {
  return (
    <>
      <Head>
        <title>Cookie Policy</title>
        <script
          id="CookieDeclaration"
          src="https://consent.cookiebot.com/9bb0d01f-9665-4d89-b526-107da1eb142f/cd.js"
          type="text/javascript"
        />
      </Head>
      <div>
        <h1>Cookie Policy</h1>
        <p>This website uses cookies to ensure you get the best experience.</p>
        {/* Cookie declaration will be automatically inserted here by Cookiebot */}
      </div>
    </>
  );
}
```

## Step 4: Testing Implementation

1. **Clear browser cookies and cache**
2. **Visit your website** - consent banner should appear
3. **Deny all cookies** - check that no tracking pixels fire
4. **Accept marketing cookies** - verify Facebook Pixel fires
5. **Accept statistics cookies** - verify Google Analytics fires
6. **Use browser dev tools** to monitor network requests and console logs

### Debug Console Commands

```javascript
// Check consent status
console.log('Cookiebot consent:', window.Cookiebot?.consent);

// Check if specific pixels are loaded
console.log('Facebook Pixel:', typeof window.fbq);
console.log('Google Analytics:', typeof window.ga);
console.log('GTM:', typeof window.dataLayer);
```

## Step 5: Common Pixel Configurations

### Facebook Pixel with Consent

```javascript
// Only initialize if marketing consent given
if (window.Cookiebot?.consent?.marketing) {
  // Facebook Pixel Code
  !function(f,b,e,v,n,t,s){...}(window, document,'script','https://connect.facebook.net/en_US/fbevents.js');
  fbq('init', 'YOUR_PIXEL_ID');
  fbq('track', 'PageView');
}
```

### Google Analytics with Consent

```javascript
// Only initialize if statistics consent given
if (window.Cookiebot?.consent?.statistics) {
  // Google Analytics Code
  gtag('config', 'GA_TRACKING_ID');
}
```

### TikTok Pixel with Consent

```javascript
// Only initialize if marketing consent given
if (window.Cookiebot?.consent?.marketing) {
  !function (w, d, t) {
    w.TiktokAnalyticsObject=t;var ttq=w[t]=w[t]||[];ttq.methods=["page","track","identify","instances","debug","on","off","once","ready","alias","group","enableCookie","disableCookie"],ttq.setAndDefer=function(t,e){t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}};for(var i=0;i<ttq.methods.length;i++)ttq.setAndDefer(ttq,ttq.methods[i]);ttq.instance=function(t){for(var e=ttq._i[t]||[],n=0;n<ttq.methods.length;n++)ttq.setAndDefer(e,ttq.methods[n]);return e},ttq.load=function(e,n){var i="https://analytics.tiktok.com/i18n/pixel/events.js";ttq._i=ttq._i||{},ttq._i[e]=[],ttq._i[e]._u=i,ttq._t=ttq._t||{},ttq._t[e]=+new Date,ttq._o=ttq._o||{},ttq._o[e]=n||{};var o=document.createElement("script");o.type="text/javascript",o.async=!0,o.src=i+"?sdkid="+e+"&lib="+t;var a=document.getElementsByTagName("script")[0];a.parentNode.insertBefore(o,a)};
  }(window, document, 'ttq');
  ttq.load('YOUR_PIXEL_ID');
  ttq.page();
}
```

## Additional Configuration

### Customize Banner Appearance

Add to your Cookiebot admin panel or via JavaScript:

```javascript
window.addEventListener('CookiebotOnLoad', function () {
  // Customize banner text, colors, etc.
  // This is configured in the Cookiebot admin panel
}, false);
```

### Handle Consent Changes

```javascript
window.addEventListener('CookiebotOnConsentChanged', function (e) {
  if (e.detail.marketing) {
    // User just accepted marketing cookies
    initMarketingPixels();
  } else {
    // User revoked marketing consent
    clearMarketingData();
  }
}, false);
```

## Troubleshooting

**Common Issues:**
1. **Pixels firing before consent** - Ensure scripts have `data-cookieconsent` attributes
2. **Banner not showing** - Check Cookiebot ID is correct
3. **Consent not persisting** - Verify domain is registered in Cookiebot admin
4. **Next.js hydration issues** - Use `useEffect` for pixel initialization

**Testing Tools:**
- Browser DevTools Network tab
- Cookiebot admin panel testing tools  
- GDPR compliance checkers

## Production Checklist

- [ ] Cookiebot script added to all pages
- [ ] All tracking pixels have consent controls
- [ ] Cookie declaration page created and linked
- [ ] Privacy policy updated
- [ ] Testing completed across different consent scenarios
- [ ] Domain registered in Cookiebot admin panel
- [ ] Banner styling matches site design

## Resources

- [Cookiebot Documentation](https://support.cookiebot.com/)
- [GDPR Compliance Guide](https://support.cookiebot.com/hc/en-us/categories/************-Compliance)
- [Next.js Implementation Examples](https://nextjs.org/docs/pages/building-your-application/optimizing/third-party-libraries)