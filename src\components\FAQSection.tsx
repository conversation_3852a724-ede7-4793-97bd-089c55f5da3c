'use client';

import { useState } from 'react'
import { motion } from 'framer-motion';

interface FAQItem {
  question: string;
  answer: string;
}

const faqItems: FAQItem[] = [
  {
    question: "Wat is de goedkoopste dierenverzekering in Nederland?",
    answer: "Kattenverzekeringen zijn er al vanaf ongeveer €10 per maand, hondenverzekeringen starten rond €15 maandelijks. De exacte premie hangt af van factoren zoals diersoort, ras, leeftijd en gewenste dekking. Gebruik onze vergelijker voor actuele tarieven."
  },
  {
    question: "Welke dierenverzekering heeft de beste dekking?",
    answer: "Figo biedt momenteel de meest uitgebreide dekking met vergoeding tot €7.000 per jaar. OHRA heeft de beste prijs-kwaliteit verhouding. De beste keuze hangt af van jouw budget en behoeften."
  },
  {
    question: "Kan ik mijn dierenverzekering overstappen?",
    answer: "Ja, je kunt jaarlijks overstappen naar een betere dierenverzekering. Let op opzegtermijnen en wachttijden bij nieuwe verzekeraars. Wij helpen je gratis met overstappen."
  },
  {
    question: "Wat vergoedt een dierenverzekering precies?",
    answer: "Een dierenverzekering helpt bij het dekken van veterinaire kosten voor jouw huisdier. Dit omvat consulten bij de dierenarts, medische behandelingen, operaties en vaak ook preventieve zorg zoals vaccinaties."
  },
  {
    question: "Waarom is het verstandig om dierenverzekeringen te vergelijken?",
    answer: "Iedere aanbieder hanteert verschillende voorwaarden, tarieven en dekkingsopties. Door te vergelijken ontdek je welke verzekering het beste aansluit bij jouw situatie en budget, zodat je de optimale bescherming krijgt voor jouw huisdier."
  },
  {
    question: "Welke aanbieders van dierenverzekeringen zijn er in Nederland?",
    answer: "In Nederland kun je terecht bij verschillende gespecialiseerde aanbieders zoals Figo, OHRA, PetSecur en Univé. Elke verzekeraar heeft eigen voorwaarden, tarieven en dekkingsmogelijkheden. Daarom is vergelijken essentieel."
  },
  {
    question: "Is een huisdierenverzekering verplicht?",
    answer: "Nee, het afsluiten van een huisdierenverzekering is niet wettelijk verplicht in Nederland. Wel is het een verstandige keuze om jezelf te beschermen tegen onverwachte hoge dierenartskosten."
  },
  {
    question: "Vanaf welke leeftijd kan ik mijn huisdier verzekeren?",
    answer: "De meeste verzekeraars accepteren huisdieren vanaf 6-8 weken oud. Voor oudere dieren (meestal 8+ jaar) kunnen er beperkingen gelden. Sluit daarom liever vroeg dan laat een verzekering af."
  }
];

interface FAQAccordionProps {
  item: FAQItem;
  isOpen: boolean;
  onToggle: () => void;
}

function FAQAccordion({ item, isOpen, onToggle }: FAQAccordionProps) {
  return (
    <div className="border-b border-gray-200 last:border-b-0">
      <button
        className="w-full text-left px-4 sm:px-6 py-4 sm:py-6 flex justify-between items-center hover:bg-gray-50 focus:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-[#2F2E51] focus:ring-inset transition-colors group"
        onClick={onToggle}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onToggle();
          }
        }}
        aria-expanded={isOpen}
        aria-controls={`faq-answer-${item.question.replace(/\s+/g, '-').toLowerCase()}`}
        id={`faq-button-${item.question.replace(/\s+/g, '-').toLowerCase()}`}
      >
        <h3 className="text-base sm:text-lg lg:text-xl font-semibold text-[#2F2E51] pr-3 sm:pr-4 group-hover:text-[#2F2E51]/80 transition-colors">
          {item.question}
        </h3>
        <div className={`flex-shrink-0 w-8 h-8 sm:w-10 sm:h-10 bg-[#2F2E51] text-white rounded-full flex items-center justify-center transition-all duration-300 group-hover:bg-[#2F2E51]/90 ${
          isOpen ? 'rotate-45 bg-[#2F2E51]' : 'rotate-0'
        }`}>
          <span className="text-lg sm:text-xl leading-none font-light" aria-hidden="true">+</span>
        </div>
      </button>
      
      {isOpen && (
        <div
          className="px-4 sm:px-6 pb-4 sm:pb-6 animate-in slide-in-from-top-2 duration-300"
          id={`faq-answer-${item.question.replace(/\s+/g, '-').toLowerCase()}`}
          role="region"
          aria-labelledby={`faq-button-${item.question.replace(/\s+/g, '-').toLowerCase()}`}
        >
          <p className="text-gray-700 leading-relaxed text-sm sm:text-base pr-8 sm:pr-14">
            {item.answer}
          </p>
        </div>
      )}
    </div>
  );
}

export default function FAQSection() {
  const [openItem, setOpenItem] = useState<number | null>(null);

  const toggleItem = (index: number) => {
    setOpenItem(openItem === index ? null : index);
  };

  // FAQ Schema markup
  const faqSchema = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqItems.map(item => ({
      "@type": "Question",
      "name": item.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": item.answer
      }
    }))
  };

  return (
    <section className="relative">
      {/* FAQ Schema Markup */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(faqSchema)
        }}
      />
      {/* Top Wave */}
      <div className="absolute top-0 left-0 w-full overflow-hidden leading-none">
        <svg 
          className="relative block w-full h-8 sm:h-12 lg:h-16" 
          data-name="Layer 1" 
          xmlns="http://www.w3.org/2000/svg" 
          viewBox="0 0 1200 120" 
          preserveAspectRatio="none"
        >
          <path 
            d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z" 
            fill="#FFF5ED"
          ></path>
        </svg>
      </div>

      <div className="py-12 sm:py-16 lg:py-24 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-8 sm:mb-12"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            viewport={{ once: true, margin: "-100px" }}
          >
            <motion.h2
              className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold text-[#2F2E51] mb-4"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
              viewport={{ once: true, margin: "-100px" }}
            >
              Veelgestelde Vragen over Dierenverzekeringen
            </motion.h2>
            <motion.p
              className="text-base sm:text-lg text-gray-600 max-w-3xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
              viewport={{ once: true, margin: "-100px" }}
            >
              Onze antwoorden op de meest gestelde vragen over dierenverzekering vergelijken,
              kosten en dekking van Nederlandse verzekeraars:
            </motion.p>
          </motion.div>

          <motion.div
            className="max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6, ease: "easeOut" }}
            viewport={{ once: true, margin: "-50px" }}
          >
            <div className="space-y-0 bg-white rounded-lg border border-gray-200 overflow-hidden">
              {faqItems.map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.8 + (index * 0.1), ease: "easeOut" }}
                  viewport={{ once: true, margin: "-30px" }}
                >
                  <FAQAccordion
                    item={item}
                    isOpen={openItem === index}
                    onToggle={() => toggleItem(index)}
                  />
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>

      {/* Bottom Wave */}
      <div className="absolute bottom-0 left-0 w-full overflow-hidden leading-none rotate-180">
        <svg 
          className="relative block w-full h-8 sm:h-12 lg:h-16" 
          data-name="Layer 1" 
          xmlns="http://www.w3.org/2000/svg" 
          viewBox="0 0 1200 120" 
          preserveAspectRatio="none"
        >
          <path 
            d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z" 
            fill="#FFF5ED"
          ></path>
        </svg>
      </div>
    </section>
  );
}