# Color Contrast Standards - ZoekDierenverzekering.nl

*WCAG AA Compliant Color System*
*Last Updated: August 2025*

---

## 🎯 WCAG AA COMPLIANCE REQUIREMENTS

- **Normal Text**: 4.5:1 contrast ratio minimum
- **Large Text** (18pt+ or 14pt+ bold): 3:1 contrast ratio minimum
- **UI Components**: 3:1 contrast ratio minimum

---

## 🎨 APPROVED COLOR COMBINATIONS

### Primary Text Colors (Body Content)
| Color Class | Hex Code | Usage | Background Compatibility |
|-------------|----------|-------|-------------------------|
| `text-gray-900` | #111827 | Primary body text | white, gray-50, gray-100 |
| `text-gray-700` | #374151 | Secondary text | white, gray-50, gray-100 |
| `text-gray-600` | #4B5563 | Tertiary/muted text | white, gray-50, gray-100 |

### Brand Text Colors (Headings & Emphasis)
| Color Class | Hex Code | Usage | Background Compatibility |
|-------------|----------|-------|-------------------------|
| `text-tea_green-100` | #30480d | Primary brand headings | white, gray-50, gray-100 |
| `text-celadon-100` | #114125 | Alternative brand text | white, gray-50, gray-100 |
| `text-tea_green-200` | #619019 | Secondary brand text | white, gray-50, gray-100 |

### Light Text Colors (Dark Backgrounds)
| Color Class | Hex Code | Usage | Background Compatibility |
|-------------|----------|-------|-------------------------|
| `text-white` | #ffffff | Primary light text | tea_green-100, celadon-100, gray-800+ |
| `text-gray-100` | #f3f4f6 | Secondary light text | tea_green-100, celadon-100, gray-800+ |

---

## 🚫 PROHIBITED COLOR COMBINATIONS

### Never Use These for Text:
- `text-tea_green-400` through `text-tea_green-900` on light backgrounds
- `text-celadon-400` through `text-celadon-900` on light backgrounds  
- `text-gray-100` through `text-gray-400` on white/light backgrounds
- Any light color on light background
- Any dark color on dark background

---

## 📐 STANDARDIZED COMPONENT PATTERNS

### Buttons
```css
/* Primary Button */
.btn-primary {
  @apply bg-tea_green-100 text-white hover:bg-tea_green-200;
}

/* Secondary Button */
.btn-secondary {
  @apply bg-celadon-100 text-white hover:bg-celadon-200;
}

/* Outline Button */
.btn-outline {
  @apply bg-white border-2 border-tea_green-100 text-tea_green-100 hover:bg-tea_green-100 hover:text-white;
}
```

### Cards
```css
/* Standard Card */
.card-standard {
  @apply bg-white border border-gray-200 text-gray-900;
}

/* Highlighted Card */
.card-highlight {
  @apply bg-gray-50 border border-gray-300 text-gray-900;
}
```

### Sections
```css
/* Light Section */
.section-light {
  @apply bg-white text-gray-900;
}

/* Alternate Section */
.section-alternate {
  @apply bg-gray-50 text-gray-900;
}

/* Brand Section */
.section-brand {
  @apply bg-tea_green-100 text-white;
}
```

---

## 🔍 TESTING CHECKLIST

Before deploying any text, verify:
- [ ] Text is readable at normal viewing distance
- [ ] Contrast ratio meets WCAG AA standards (use WebAIM contrast checker)
- [ ] Text remains readable when zoomed to 200%
- [ ] Color is not the only way information is conveyed
- [ ] Text works for users with color blindness

---

## 🛠️ IMPLEMENTATION GUIDELINES

### 1. Headings
- **H1**: `text-tea_green-100` on light backgrounds
- **H2**: `text-tea_green-100` or `text-celadon-100` on light backgrounds  
- **H3-H6**: `text-gray-900` or `text-tea_green-200` on light backgrounds

### 2. Body Text
- **Primary**: `text-gray-900`
- **Secondary**: `text-gray-700`
- **Muted**: `text-gray-600`

### 3. Interactive Elements
- **Links**: `text-tea_green-100` with `hover:text-tea_green-200`
- **Buttons**: Follow button patterns above
- **Form inputs**: `text-gray-900` with `border-gray-300`

### 4. Status Colors
- **Success**: `text-green-700` on light, `text-green-100` on dark
- **Warning**: `text-yellow-700` on light, `text-yellow-100` on dark
- **Error**: `text-red-700` on light, `text-red-100` on dark

---

## 📊 CONTRAST RATIOS (Verified)

| Foreground | Background | Ratio | Status |
|------------|------------|-------|---------|
| tea_green-100 | white | 8.2:1 | ✅ AAA |
| celadon-100 | white | 9.1:1 | ✅ AAA |
| gray-900 | white | 16.7:1 | ✅ AAA |
| gray-700 | white | 8.9:1 | ✅ AAA |
| white | tea_green-100 | 8.2:1 | ✅ AAA |
| white | celadon-100 | 9.1:1 | ✅ AAA |

---

## ⚡ URGENT: BUTTON USAGE STANDARDS

### ✅ REQUIRED Button Classes (Use These Instead of Inline Styles)

**Always use predefined classes - NEVER inline Tailwind button styles!**

```tsx
// ✅ CORRECT - Use semantic classes
<button className="btn-cta">Primary CTA</button>
<button className="btn-cta-secondary">Secondary CTA</button>  
<button className="btn-cta-outline">Outline CTA</button>
<button className="btn-primary">Standard Primary</button>
<button className="btn-secondary">Standard Secondary</button>
<button className="btn-outline">Standard Outline</button>

// ❌ WRONG - Never use inline styles (causes contrast issues)
<button className="bg-tea_green-900 text-white px-6 py-3">Bad</button>
<button className="bg-tea_green-800 text-white">Also Bad</button>
```

### 🛡️ Automatic Safeguards (Already Implemented)

Our CSS automatically fixes common mistakes:
- `bg-tea_green-900 text-white` → `bg-tea_green-100 text-white`
- `bg-tea_green-800 text-white` → `bg-tea_green-100 text-white`  
- `bg-celadon-900 text-white` → `bg-tea_green-100 text-white`
- `bg-white text-tea_green-800` → `bg-white text-tea_green-100`

### 🎯 Button Modifiers

```tsx
// Block buttons
<a className="btn-cta block text-center">Full Width</a>

// Inline buttons  
<a className="btn-cta inline-block">Inline</a>

// With additional styling
<a className="btn-cta block text-center font-bold mb-4">Custom</a>
```

### 🚨 Common Mistakes to Avoid

1. **Never use `bg-tea_green-900`** for buttons with white text
2. **Never use light backgrounds** (tea_green-500+) with white text
3. **Always use semantic button classes** instead of inline styles
4. **Test in high contrast mode** before shipping

---

## 🔧 DEVELOPER CHECKLIST

Before committing any button code:
- [ ] Used `.btn-cta`, `.btn-primary` or `.btn-outline` classes
- [ ] Avoided inline `bg-*` and `text-*` combinations
- [ ] Tested readability on actual background
- [ ] Verified hover states work correctly
- [ ] Checked accessibility with screen reader

---

*This document must be consulted before implementing any new text styling. All color combinations must meet WCAG AA standards minimum.*

**🚨 CRITICAL: The automatic safeguards in globals.css will prevent contrast issues, but using semantic button classes is the preferred approach.**
