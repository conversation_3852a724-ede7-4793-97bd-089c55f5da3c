"use client"

import Image from 'next/image'
import Link from 'next/link'
import { motion } from 'framer-motion'

export default function ArticlesSection() {
  return (
    <section className="w-full bg-tea_green-800 py-8 sm:py-16 mt-4 sm:mt-0">
      <div className="max-w-[1284px] mx-auto px-4">
        {/* Title - Responsive */}
        <motion.h2
          className="text-[24px] sm:text-[32px] md:text-[42px] font-bold text-celadon-100 text-center leading-[1.2] mb-8 sm:mb-12 md:mb-16 font-figtree"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true, margin: "-100px" }}
        >
          Onze meest gelezen artikelen over
          <br />
          huisdierenverzekeringen
        </motion.h2>

        {/* Desktop Layout */}
        <div className="hidden md:grid md:grid-cols-3 gap-[30px]">
          {/* Column 1 */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1, ease: "easeOut" }}
            viewport={{ once: true, margin: "-50px" }}
          >
            <Link href="/kennisbank/dierenverzekering-verstandig" className="flex flex-col gap-[30px] cursor-pointer hover:opacity-80 transition-opacity group">
              <motion.div
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.3 }}
                className="flex flex-col gap-[30px]"
              >
                {/* Large image card */}
                <div className="w-[398px] h-[265px] rounded-[10px] overflow-hidden">
                  <Image
                    src="/images/kennisbank/verzekeren/insurance-paperwork.jpg"
                    alt="Huisdier verzekeren: verstandig of niet?"
                    width={398}
                    height={265}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    loading="lazy"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 398px"
                  />
                </div>

                {/* Text card below */}
                <div className="w-[398px] h-[90px] flex items-center">
                  <h3 className="text-[24px] font-bold text-celadon-100 leading-[1.2] font-figtree">
                    Huisdier verzekeren: verstandig of niet?
                  </h3>
                </div>
              </motion.div>
            </Link>
          </motion.div>

          {/* Column 2 */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3, ease: "easeOut" }}
            viewport={{ once: true, margin: "-50px" }}
          >
            <Link href="/kennisbank/dierenarts-kiezen" className="flex flex-col gap-[30px] cursor-pointer hover:opacity-80 transition-opacity group">
              <motion.div
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.3 }}
                className="flex flex-col gap-[30px]"
              >
                {/* Large image card */}
                <div className="w-[398px] h-[265px] rounded-[10px] overflow-hidden">
                  <Image
                    src="/images/kennisbank/bij-de-dierenarts/vet-examining-dog.jpg"
                    alt="Hoe kies je een goede dierenarts?"
                    width={398}
                    height={265}
                    className="w-full h-full object-cover"
                  />
                </div>

                {/* Text card below */}
                <div className="w-[398px] h-[61px] flex items-center">
                  <h3 className="text-[24px] font-bold text-celadon-100 leading-[1.2] font-figtree">
                    Hoe kies je een goede dierenarts?
                  </h3>
                </div>
              </motion.div>
            </Link>
          </motion.div>

          {/* Column 3 */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5, ease: "easeOut" }}
            viewport={{ once: true, margin: "-50px" }}
          >
            <Link href="/kennisbank/castratie-sterilisatie-huisdier" className="flex flex-col gap-[30px] cursor-pointer hover:opacity-80 transition-opacity group">
              <motion.div
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.3 }}
                className="flex flex-col gap-[30px]"
              >
                {/* Large image card */}
                <div className="w-[398px] h-[264px] rounded-[10px] overflow-hidden">
                  <Image
                    src="/images/kennisbank/verzekeren/family-with-dog.jpg"
                    alt="Sterilisatie of castratie van hond of kat"
                    width={398}
                    height={264}
                    className="w-full h-full object-cover"
                  />
                </div>

                {/* Text card below */}
                <div className="w-[398px] h-[61px] flex items-center">
                  <h3 className="text-[24px] font-bold text-[#2F2E51] leading-[1.2] font-figtree">
                    Sterilisatie of castratie van hond of kat
                  </h3>
                </div>
              </motion.div>
            </Link>
          </motion.div>
        </div>

        {/* Mobile Layout */}
        <div className="block md:hidden space-y-6">
          {/* Article 1 - Mobile */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1, ease: "easeOut" }}
            viewport={{ once: true, margin: "-30px" }}
          >
              <Link href="/kennisbank/dierenverzekering-verstandig" className="w-full max-w-[360px] mx-auto block cursor-pointer hover:opacity-80 transition-opacity">
                <div className="w-full h-[200px] rounded-[10px] overflow-hidden mb-4">
                  <Image
                    src="/images/kennisbank/verzekeren/insurance-paperwork.jpg"
                    alt="Huisdier verzekeren: verstandig of niet?"
                    width={360}
                    height={200}
                    className="w-full h-full object-cover"
                  />
                </div>
                <h3 className="text-[18px] sm:text-[20px] font-bold text-[#2F2E51] leading-[1.2] font-figtree px-2">
                  Huisdier verzekeren: verstandig of niet?
                </h3>
              </Link>
          </motion.div>

          {/* Article 2 - Mobile */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3, ease: "easeOut" }}
            viewport={{ once: true, margin: "-30px" }}
          >
            <Link href="/kennisbank/dierenarts-kiezen" className="w-full max-w-[360px] mx-auto block cursor-pointer hover:opacity-80 transition-opacity">
              <div className="w-full h-[200px] rounded-[10px] overflow-hidden mb-4">
                <Image
                  src="/images/kennisbank/bij-de-dierenarts/vet-examining-dog.jpg"
                  alt="Hoe kies je een goede dierenarts?"
                  width={360}
                  height={200}
                  className="w-full h-full object-cover"
                />
              </div>
              <h3 className="text-[18px] sm:text-[20px] font-bold text-[#2F2E51] leading-[1.2] font-figtree px-2">
                Hoe kies je een goede dierenarts?
              </h3>
            </Link>
          </motion.div>

          {/* Article 3 - Mobile */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5, ease: "easeOut" }}
            viewport={{ once: true, margin: "-30px" }}
          >
            <Link href="/kennisbank/castratie-sterilisatie-huisdier" className="w-full max-w-[360px] mx-auto block cursor-pointer hover:opacity-80 transition-opacity">
              <div className="w-full h-[200px] rounded-[10px] overflow-hidden mb-4">
                <Image
                  src="/images/kennisbank/verzekeren/family-with-dog.jpg"
                  alt="Sterilisatie of castratie van hond of kat"
                  width={360}
                  height={200}
                  className="w-full h-full object-cover"
                />
              </div>
              <h3 className="text-[18px] sm:text-[20px] font-bold text-[#2F2E51] leading-[1.2] font-figtree px-2">
                Sterilisatie of castratie van hond of kat
              </h3>
            </Link>
          </motion.div>
        </div>

        {/* View all button - Responsive */}
        <motion.div
          className="flex justify-center mt-8 sm:mt-12 md:mt-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.7, ease: "easeOut" }}
          viewport={{ once: true, margin: "-50px" }}
        >
          <Link href="/kennisbank">
            <motion.button
              className="bg-white border border-celadon-100 text-celadon-100 rounded-full font-bold text-[16px] sm:text-[19px] hover:bg-celadon-100 hover:text-white transition-colors font-figtree w-[150px] sm:w-[176px] h-[50px] sm:h-[61px] flex items-center justify-center"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={{ duration: 0.2 }}
            >
              Alle artikelen
            </motion.button>
          </Link>
        </motion.div>
      </div>
    </section>
  )
}