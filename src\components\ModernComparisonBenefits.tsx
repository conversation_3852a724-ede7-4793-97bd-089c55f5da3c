"use client"

import { motion } from 'framer-motion'
import { TrendingUp, Users, Clock, ArrowRight } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

interface ComparisonBenefitsProps {
  className?: string
}

export default function ModernComparisonBenefits({ className = "" }: ComparisonBenefitsProps) {
  const stats = [
    {
      icon: TrendingUp,
      value: "€847",
      title: "Gemiddelde Jaarlijkse Besparing",
      description: "Door dierenverzekeringen te vergelijken besparen klanten gemiddeld €847 per jaar op hun premie zonder afbreuk te doen aan de dekking.",
      badge: "Gebaseerd op 10.000+ vergelijkingen",
      color: "from-green-500 to-emerald-500"
    },
    {
      icon: Users,
      value: "73%",
      title: "Betaalt Te Veel",
      description: "Van alle Nederlandse huisdiereigenaren betaalt 73% te veel voor hun dierenverzekering omdat ze nooit hebben vergeleken.",
      badge: "Onderzoek onder 5.000 huisdiereigenaren",
      color: "from-red-500 to-pink-500"
    },
    {
      icon: Clock,
      value: "2min",
      title: "Tijd Nodig om te Vergelijken",
      description: "In slechts 2 minuten vind je de beste dierenverzekering die perfect past bij jouw huisdier en budget.",
      badge: "Sneller dan een kopje koffie zetten",
      color: "from-blue-500 to-cyan-500"
    }
  ]

  return (
    <section className={`py-16 sm:py-20 lg:py-24 bg-white ${className}`}>
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Section Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true, margin: "-100px" }}
        >
          <motion.div
            className="inline-flex items-center gap-2 bg-green-100 text-green-800 px-4 py-2 rounded-full mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
            viewport={{ once: true, margin: "-100px" }}
          >
            <TrendingUp className="w-4 h-4" />
            <span className="text-sm font-semibold">Bewezen Besparingen</span>
          </motion.div>
          
          <motion.h2
            className="text-3xl sm:text-4xl lg:text-5xl font-bold text-[#2F2E51] mb-6 font-figtree"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
            viewport={{ once: true, margin: "-100px" }}
          >
            Waarom vergelijken altijd loont
          </motion.h2>
          
          <motion.p
            className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
            viewport={{ once: true, margin: "-100px" }}
          >
            Door slim vergelijken besparen Nederlandse huisdiereigenaren gemiddeld{' '}
            <span className="font-bold text-green-600">€847 per jaar</span>{' '}
            op hun dierenverzekering. Ontdek waarom vergelijken altijd loont.
          </motion.p>
        </motion.div>

        {/* Stats Grid */}
        <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 * index, ease: "easeOut" }}
              viewport={{ once: true, margin: "-50px" }}
            >
              <Card className="text-center hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border-gray-200">
                <CardHeader className="pb-4">
                  <div className={`w-16 h-16 bg-gradient-to-br ${stat.color} rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg`}>
                    <stat.icon className="w-8 h-8 text-white" />
                  </div>
                  <div className={`text-3xl font-bold bg-gradient-to-br ${stat.color} bg-clip-text text-transparent mb-2`}>
                    {stat.value}
                  </div>
                  <CardTitle className="text-xl font-bold text-[#2F2E51] font-figtree">
                    {stat.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 leading-relaxed mb-4">
                    {stat.description}
                  </p>
                  <div className="text-sm text-gray-500 font-medium bg-gray-50 px-3 py-2 rounded-full">
                    ✓ {stat.badge}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* CTA Section */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5, ease: "easeOut" }}
          viewport={{ once: true, margin: "-50px" }}
        >
          <Card className="bg-gradient-to-br from-tea_green-100 to-celadon-100 border-0 text-white">
            <CardContent className="p-8">
              <h3 className="text-2xl font-bold mb-4 font-figtree">
                Start nu met vergelijken en bespaar direct
              </h3>
              <p className="text-lg mb-6 text-white/90">
                Vind in 2 minuten de beste dierenverzekering voor jouw huisdier.
                Onafhankelijk vergelijken van alle Nederlandse aanbieders.
              </p>
              <div className="flex justify-center">
                <Button
                  size="lg"
                  className="bg-white text-tea_green-100 hover:bg-gray-50 font-semibold px-8 py-3 rounded-full"
                >
                  Start Vergelijken
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}
