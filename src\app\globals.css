@tailwind base;
@tailwind components;
@tailwind utilities;

/* WCAG AA Compliant Color Standards */
@layer components {
  /* Primary Button - WCAG AA Compliant */
  .btn-primary {
    @apply bg-tea_green-100 text-white hover:bg-tea_green-200 transition-colors;
  }

  /* Secondary Button - WCAG AA Compliant */
  .btn-secondary {
    @apply bg-celadon-100 text-white hover:bg-celadon-200 transition-colors;
  }

  /* Outline Button - WCAG AA Compliant */
  .btn-outline {
    @apply bg-white border-2 border-tea_green-100 text-tea_green-100 hover:bg-tea_green-100 hover:text-white transition-colors;
  }

  /* Standard Card - WCAG AA Compliant */
  .card-standard {
    @apply bg-white border border-gray-200 text-gray-900;
  }

  /* Highlighted Card - WCAG AA Compliant */
  .card-highlight {
    @apply bg-gray-50 border border-gray-300 text-gray-900;
  }

  /* Light Section - WCAG AA Compliant */
  .section-light {
    @apply bg-white text-gray-900;
  }

  /* Alternate Section - WCAG AA Compliant */
  .section-alternate {
    @apply bg-gray-50 text-gray-900;
  }

  /* Brand Section - WCAG AA Compliant */
  .section-brand {
    @apply bg-tea_green-100 text-white;
  }

  /* Heading Styles - WCAG AA Compliant */
  .heading-primary {
    @apply text-tea_green-100;
  }

  .heading-secondary {
    @apply text-celadon-100;
  }

  /* Text Styles - WCAG AA Compliant */
  .text-primary {
    @apply text-gray-900;
  }

  .text-secondary {
    @apply text-gray-700;
  }

  .text-muted {
    @apply text-gray-600;
  }

  /* Link Styles - WCAG AA Compliant */
  .link-primary {
    @apply text-tea_green-100 hover:text-tea_green-200 transition-colors;
  }

  .link-secondary {
    @apply text-celadon-100 hover:text-celadon-200 transition-colors;
  }

  /* CTA Button Styles - Prevent Poor Contrast */
  .btn-cta {
    @apply bg-tea_green-100 text-white px-6 py-3 rounded-full font-semibold hover:bg-tea_green-200 transition-colors;
  }

  .btn-cta-secondary {
    @apply bg-celadon-100 text-white px-6 py-3 rounded-full font-semibold hover:bg-celadon-200 transition-colors;
  }

  .btn-cta-outline {
    @apply bg-white border-2 border-tea_green-100 text-tea_green-100 px-6 py-3 rounded-full font-semibold hover:bg-tea_green-100 hover:text-white transition-colors;
  }
}

/* ACCESSIBILITY SAFEGUARDS - Prevent Poor Contrast Combinations */
@layer utilities {
  /* Override dangerous light background + white text combinations */
  .bg-tea_green-900.text-white,
  .bg-tea_green-800.text-white,
  .bg-tea_green-700.text-white,
  .bg-tea_green-600.text-white,
  .bg-tea_green-500.text-white,
  .bg-tea_green-400.text-white,
  .bg-celadon-900.text-white,
  .bg-celadon-800.text-white,
  .bg-celadon-700.text-white,
  .bg-celadon-600.text-white,
  .bg-celadon-500.text-white,
  .bg-celadon-400.text-white {
    background-color: #30480d !important; /* tea_green-100 */
    color: #ffffff !important; /* white */
  }

  /* Override dangerous light background + light text combinations */
  .bg-white.text-tea_green-400,
  .bg-white.text-tea_green-500,
  .bg-white.text-tea_green-600,
  .bg-white.text-tea_green-700,
  .bg-white.text-tea_green-800,
  .bg-white.text-tea_green-900,
  .bg-gray-50.text-tea_green-400,
  .bg-gray-50.text-tea_green-500,
  .bg-gray-50.text-tea_green-600,
  .bg-gray-50.text-tea_green-700,
  .bg-gray-50.text-tea_green-800,
  .bg-gray-50.text-tea_green-900 {
    color: #30480d !important; /* tea_green-100 */
  }

  /* Force proper hover states for overridden combinations */
  .bg-tea_green-900.text-white:hover,
  .bg-tea_green-800.text-white:hover,
  .bg-tea_green-700.text-white:hover,
  .bg-tea_green-600.text-white:hover,
  .bg-tea_green-500.text-white:hover,
  .bg-tea_green-400.text-white:hover {
    background-color: #619019 !important; /* tea_green-200 */
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem
  }
  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}