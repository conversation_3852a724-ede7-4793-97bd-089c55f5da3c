/** @type {import('tailwindcss').Config} */
/**
 * WCAG AA COMPLIANT COLOR SYSTEM
 *
 * APPROVED TEXT COLORS (on light backgrounds):
 * - text-tea_green-100 (#30480d) - Primary brand headings
 * - text-celadon-100 (#114125) - Alternative brand text
 * - text-gray-900 (#111827) - Primary body text
 * - text-gray-700 (#374151) - Secondary text
 * - text-gray-600 (#4B5563) - Tertiary/muted text
 *
 * APPROVED BACKGROUND COLORS:
 * - bg-white - Primary background
 * - bg-gray-50 - Light section background
 * - bg-tea_green-100 - Dark brand background (use white text)
 * - bg-celadon-100 - Alternative dark background (use white text)
 *
 * PROHIBITED COMBINATIONS:
 * - Never use tea_green-400+ or celadon-400+ for text on light backgrounds
 * - Never use light colors on light backgrounds
 * - Always verify contrast ratio meets 4.5:1 minimum
 */
module.exports = {
    darkMode: ['class'],
    content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './src/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
  	extend: {
  		fontFamily: {
  			sans: [
  				'var(--font-poppins)',
  				'system-ui',
  				'sans-serif'
  			],
  			figtree: [
  				'var(--font-figtree)',
  				'system-ui',
  				'sans-serif'
  			]
  		},
  		colors: {
  			celeste: {
  				'100': '#0b4a4b',
  				'200': '#179597',
  				'300': '#26dbdf',
  				'400': '#71e8ea',
  				'500': '#bcf4f5',
  				'600': '#caf6f7',
  				'700': '#d7f8f9',
  				'800': '#e4fbfb',
  				'900': '#f2fdfd',
  				DEFAULT: '#bcf4f5'
  			},
  			celadon: {
  				'100': '#114125',
  				'200': '#238349',
  				'300': '#34c46e',
  				'400': '#71d99b',
  				'500': '#b4ebca',
  				'600': '#c2efd4',
  				'700': '#d1f3df',
  				'800': '#e0f7e9',
  				'900': '#f0fbf4',
  				DEFAULT: '#b4ebca'
  			},
  			tea_green: {
  				'100': '#30480d',
  				'200': '#619019',
  				'300': '#91d826',
  				'400': '#b5e56d',
  				'500': '#d9f2b4',
  				'600': '#e1f5c4',
  				'700': '#e9f7d3',
  				'800': '#f0fae2',
  				'900': '#f8fcf0',
  				DEFAULT: '#d9f2b4'
  			},
  			tea_green_alt: {
  				'100': '#195307',
  				'200': '#32a50e',
  				'300': '#51ec22',
  				'400': '#92f374',
  				'500': '#d3fac7',
  				'600': '#dcfbd2',
  				'700': '#e4fcdd',
  				'800': '#edfde8',
  				'900': '#f6fef4',
  				DEFAULT: '#d3fac7'
  			},
  			cherry_blossom_pink: {
  				'100': '#58000f',
  				'200': '#af001d',
  				'300': '#ff0831',
  				'400': '#ff607a',
  				'500': '#ffb7c3',
  				'600': '#ffc6cf',
  				'700': '#ffd4db',
  				'800': '#ffe2e7',
  				'900': '#fff1f3',
  				DEFAULT: '#ffb7c3'
  			},
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			}
  		},
  		backgroundImage: {
  			'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
  			'gradient-diamond': 'radial-gradient(ellipse at 50% 100%, var(--tw-gradient-stops))'
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		}
  	}
  },
  plugins: [require("tailwindcss-animate")],
}