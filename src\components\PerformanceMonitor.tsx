'use client'

import { useEffect } from 'react'

interface PerformanceMetrics {
  lcp?: number
  fid?: number
  cls?: number
  fcp?: number
  ttfb?: number
}

interface PerformanceEntryWithProcessingStart extends PerformanceEntry {
  processingStart: number
}

interface LayoutShiftEntry extends PerformanceEntry {
  hadRecentInput: boolean
  value: number
}

interface WindowWithGtag extends Window {
  gtag?: (command: string, eventName: string, parameters: Record<string, unknown>) => void
}

export default function PerformanceMonitor() {
  useEffect(() => {
    // Only run in production
    if (process.env.NODE_ENV !== 'production') return

    const metrics: PerformanceMetrics = {}

    // Measure Core Web Vitals
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        switch (entry.entryType) {
          case 'largest-contentful-paint':
            metrics.lcp = entry.startTime
            break
          case 'first-input':
            metrics.fid = (entry as PerformanceEntryWithProcessingStart).processingStart - entry.startTime
            break
          case 'layout-shift':
            if (!(entry as LayoutShiftEntry).hadRecentInput) {
              metrics.cls = (metrics.cls || 0) + (entry as LayoutShiftEntry).value
            }
            break
        }
      }
    })

    // Observe Core Web Vitals
    try {
      observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] })
    } catch {
      // Fallback for browsers that don't support all entry types
      console.warn('Performance monitoring not fully supported')
    }

    // Measure other performance metrics
    const measureOtherMetrics = () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      if (navigation) {
        metrics.fcp = navigation.responseStart - navigation.fetchStart
        metrics.ttfb = navigation.responseStart - navigation.requestStart
      }
    }

    // Wait for page load to measure metrics
    if (document.readyState === 'complete') {
      measureOtherMetrics()
    } else {
      window.addEventListener('load', measureOtherMetrics)
    }

    // Send metrics to analytics (placeholder)
    const sendMetrics = () => {
      if (Object.keys(metrics).length > 0) {
        // In a real app, you would send these to your analytics service
        console.log('Performance Metrics:', metrics)
        
        // Example: Send to Google Analytics 4
        if (typeof window !== 'undefined' && 'gtag' in window) {
          Object.entries(metrics).forEach(([metric, value]) => {
            if (value !== undefined) {
              (window as WindowWithGtag).gtag?.('event', metric, {
                value: Math.round(value),
                metric_id: metric,
              })
            }
          })
        }
      }
    }

    // Send metrics after a delay to ensure all measurements are captured
    const timeoutId = setTimeout(sendMetrics, 5000)

    return () => {
      observer.disconnect()
      clearTimeout(timeoutId)
      window.removeEventListener('load', measureOtherMetrics)
    }
  }, [])

  return null // This component doesn't render anything
}
