"use client"

import { motion } from 'framer-motion'
import Image from 'next/image'

export default function TrustSection() {
  return (
    <section className="py-16 sm:py-20 lg:py-24 bg-[#FFF5ED]">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true, margin: "-100px" }}
        >
          <motion.h2
            className="text-3xl sm:text-4xl lg:text-5xl font-bold text-[#2F2E51] mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
            viewport={{ once: true, margin: "-100px" }}
          >
            Vertrouwd door 50.000+ <PERSON><PERSON><PERSON> huisdiereigenaren
          </motion.h2>
          <motion.p
            className="text-lg text-gray-600 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
            viewport={{ once: true, margin: "-100px" }}
          >
            Ontdek waarom duizenden Nederlanders kiezen voor ZoekDierenverzekering.nl
            om de beste dierenverzekering te vinden.
          </motion.p>
        </motion.div>

        {/* Trust Stats */}
        <div className="grid sm:grid-cols-3 gap-8 mb-16">
          {/* Stat 1 */}
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6, ease: "easeOut" }}
            viewport={{ once: true, margin: "-50px" }}
          >
            <motion.div
              className="w-24 h-24 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4"
              whileHover={{ scale: 1.1 }}
              transition={{ duration: 0.3 }}
            >
              <span className="text-2xl font-bold text-white">€2.3M+</span>
            </motion.div>
            <h3 className="text-xl font-bold text-[#2F2E51] mb-2">Besparingen Gerealiseerd</h3>
            <p className="text-gray-600">
              Totaal aan besparingen die onze klanten hebben gerealiseerd door slim vergelijken
            </p>
          </motion.div>

          {/* Stat 2 */}
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8, ease: "easeOut" }}
            viewport={{ once: true, margin: "-50px" }}
          >
            <motion.div
              className="w-24 h-24 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4"
              whileHover={{ scale: 1.1 }}
              transition={{ duration: 0.3 }}
            >
              <span className="text-2xl font-bold text-white">4.8★</span>
            </motion.div>
            <h3 className="text-xl font-bold text-[#2F2E51] mb-2">Gemiddelde Beoordeling</h3>
            <p className="text-gray-600">
              Gebaseerd op 1.247 beoordelingen van tevreden klanten in heel Nederland
            </p>
          </motion.div>

          {/* Stat 3 */}
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.0, ease: "easeOut" }}
            viewport={{ once: true, margin: "-50px" }}
          >
            <motion.div
              className="w-24 h-24 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4"
              whileHover={{ scale: 1.1 }}
              transition={{ duration: 0.3 }}
            >
              <span className="text-2xl font-bold text-white">98%</span>
            </motion.div>
            <h3 className="text-xl font-bold text-[#2F2E51] mb-2">Klantentevredenheid</h3>
            <p className="text-gray-600">
              Van onze klanten is tevreden met de service en zou ons aanbevelen aan anderen
            </p>
          </motion.div>
        </div>

        {/* Why Choose Us Section */}
        <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-8 lg:p-12 mb-16">
          <h3 className="text-2xl sm:text-3xl font-bold text-center text-[#2F2E51] mb-8">
            Daarom kies je voor een dierenverzekering
          </h3>
          
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left content */}
            <div>
              <p className="text-lg text-gray-700 mb-6 leading-relaxed">
                Een huisdier brengt zorgkosten met zich mee. Iedere hond of kat moet wel eens naar de dokter, 
                net als jijzelf. Voorkom financiële verrassingen bij de dierenarts en sluit een passende 
                dierenverzekering af voor je trouwe dierenvriend.
              </p>

              <ul className="space-y-4">
                <li className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <span className="text-gray-700 font-medium">Vergelijk álle Nederlandse verzekeraars</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <span className="text-gray-700 font-medium">Alle kleine lettertjes op een rij</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <span className="text-gray-700 font-medium">Reviews van andere huisdiereigenaren</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <span className="text-gray-700 font-medium">Gratis persoonlijk advies van experts</span>
                </li>
              </ul>
            </div>

            {/* Right content - Image */}
            <div className="relative">
              <div className="w-full h-80 rounded-xl shadow-lg overflow-hidden">
                <Image 
                  src="/images/man-about-to-kiss-a-cute-puppy-dog-outdoors-2025-03-07-23-33-25-utc.webp" 
                  alt="Man about to kiss a cute puppy"
                  className="w-full h-full object-cover"
                  width={400}
                  height={320}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Smart Comparison Section */}
        <div className="grid lg:grid-cols-2 gap-12">
          {/* Left side */}
          <div>
            <h3 className="text-2xl sm:text-3xl font-bold text-[#2F2E51] mb-6">
              Slim vergelijken op ZoekDierenverzekering.nl
            </h3>
            <p className="text-lg text-gray-600 mb-8 leading-relaxed">
              Bekijk, vergelijk en kies in een paar klikken de perfecte verzekering voor jouw viervoeter. 
              Onze vergelijker toont alle Nederlandse dierenverzekeraars met actuele premies en voorwaarden.
            </p>

            <div className="space-y-6">
              {/* Feature 1 */}
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center flex-shrink-0">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <h4 className="text-lg font-bold text-[#2F2E51] mb-2">Actuele en correcte informatie</h4>
                  <p className="text-gray-600">
                    Je huisdier is een belangrijk onderdeel van je gezin. Samen met onze experts zorgen we 
                    ervoor dat alle verzekeringsvoorwaarden, premies en andere gegevens actueel zijn.
                  </p>
                </div>
              </div>

              {/* Feature 2 */}
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center flex-shrink-0">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
                <div>
                  <h4 className="text-lg font-bold text-[#2F2E51] mb-2">Onafhankelijk vergelijken</h4>
                  <p className="text-gray-600">
                    ZoekDierenverzekering.nl is een onafhankelijk informatie- en vergelijkingsplatform.
                    In onze vergelijker vind je álle huisdierenverzekeringen van Nederland.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Right side - Stats */}
          <div className="bg-gradient-to-br from-[#2F2E51] to-slate-700 rounded-2xl p-8 text-white">
            <h4 className="text-2xl font-bold mb-6">Huisdieren in Nederland</h4>
            <p className="text-blue-100 mb-6">Wist je dat…</p>
            
            <ul className="space-y-4">
              <li className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-400 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-white text-xs">✓</span>
                </div>
                <span className="text-blue-50">
                  …Nederland ongeveer 1,9 miljoen honden en ruim 3 miljoen katten telt?
                </span>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-400 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-white text-xs">✓</span>
                </div>
                <span className="text-blue-50">
                  …naar schatting één op de twintig huisdieren in ons land verzekerd is?
                </span>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-400 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-white text-xs">✓</span>
                </div>
                <span className="text-blue-50">
                  …het verzekeren van een huisdier al mogelijk is vanaf zo&rsquo;n €10 per maand?
                </span>
              </li>
            </ul>

            {/* CTA in stats box */}
            <div className="mt-8 p-4 bg-white/10 rounded-xl border border-white/20">
              <div className="text-sm text-blue-100 mb-2">Bespaar gemiddeld</div>
              <div className="text-3xl font-bold text-white mb-2">€847 per jaar</div>
              <div className="text-sm text-blue-100">door slim vergelijken</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}