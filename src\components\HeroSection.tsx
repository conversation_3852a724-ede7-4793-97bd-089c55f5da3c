"use client"

import Navigation from "./Navigation";
import Image from "next/image";
import { motion } from "framer-motion";

export default function HeroSection() {
  return (
    <section className="relative bg-tea_green-900">
      {/* Navigation */}
      <Navigation />
      
      {/* Main content */}
      <div className="pt-24 pb-8 sm:pt-28 lg:pt-32 sm:pb-12 lg:pb-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-8">
          <div className="grid lg:grid-cols-2 gap-8 lg:gap-16 xl:gap-20 items-center">
            
            {/* Left content */}
            <motion.div
              className="space-y-3 sm:space-y-4 lg:space-y-5 lg:pr-8 xl:pr-12"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
            >
              {/* Main heading */}
              <div className="space-y-2 sm:space-y-2 lg:space-y-3">
                <motion.h1
                  className="text-3xl sm:text-4xl font-bold text-celadon-100 leading-tight lg:leading-[1.05] lg:tracking-[-0.02em] lg:max-w-[40ch] lg:[text-wrap:balance] lg:text-[clamp(36px,4.5vw,56px)]"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
                >
                  Dierenverzekering Vergelijken
                </motion.h1>
                <motion.h2
                  className="text-lg sm:text-xl lg:text-xl font-semibold text-celadon-100 mt-4 lg:mt-4 max-w-2xl lg:max-w-3xl xl:max-w-4xl"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
                >
                  Beste Dierenverzekeringen Nederland - Onafhankelijk Vergelijken
                </motion.h2>
                <motion.p
                  className="text-base sm:text-lg text-celadon-300 max-w-2xl lg:max-w-3xl xl:max-w-4xl mt-6 lg:mt-6 leading-relaxed"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.6, ease: "easeOut" }}
                >
                  Ontdek en vergelijk alle Nederlandse dierenverzekeraars op één centrale plek. Vind de ideale
                  verzekering voor jouw hond of kat met tarieven vanaf €10 per maand. Volledig onafhankelijk en betrouwbaar advies
                  van verzekeringsexperts.
                </motion.p>
              </div>


            </motion.div>

            {/* Right content - Hero image */}
            <motion.div
              className="relative flex justify-center lg:justify-end mt-6 lg:mt-0 order-first lg:order-none lg:pl-8 xl:pl-12"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 1, delay: 0.3, ease: "easeOut" }}
            >
              {/* Paw decoration */}
              <motion.div
                className="absolute top-2 left-4 sm:top-4 sm:left-8 w-6 h-6 sm:w-8 sm:h-8 z-10"
                initial={{ opacity: 0, rotate: -10 }}
                animate={{ opacity: 1, rotate: 0 }}
                transition={{ duration: 0.8, delay: 1.2, ease: "easeOut" }}
              >
                <svg viewBox="0 0 40 40" className="w-full h-full" fill="none">
                  <g fill="#F2D1B0">
                    <ellipse cx="20" cy="28" rx="6" ry="4"/>
                    <circle cx="12" cy="12" r="3"/>
                    <circle cx="18" cy="9" r="2.5"/>
                    <circle cx="28" cy="12" r="3"/>
                    <circle cx="24" cy="16" r="2.5"/>
                  </g>
                </svg>
              </motion.div>

              {/* Main hero image - circular */}
              <motion.div
                className="w-48 h-48 sm:w-56 sm:h-56 lg:w-64 lg:h-64 rounded-full bg-gradient-to-br from-celeste-400 via-celeste-300 to-celeste-200 p-1 shadow-lg"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.3 }}
              >
                <div className="w-full h-full rounded-full bg-white flex items-center justify-center overflow-hidden">
                  <Image
                    src="/images/hero.webp"
                    alt="Dierenverzekering vergelijken Nederland - hond en kat verzekeren vanaf €10 per maand"
                    title="ZoekDierenverzekering.nl - Onafhankelijk vergelijken van alle Nederlandse dierenverzekeraars"
                    width={256}
                    height={256}
                    className="w-full h-full object-cover rounded-full"
                    priority
                  />
                </div>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}