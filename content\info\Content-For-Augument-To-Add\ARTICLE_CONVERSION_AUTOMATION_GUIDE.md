# Article Conversion Automation Guide

This document provides step-by-step instructions for converting scraped content into properly formatted MDX articles for the ZoekDierenverzekering.nl kennisbank.

## 📋 Overview

This process converts raw scraped content into SEO-optimized, properly formatted MDX articles that integrate seamlessly with the existing kennisbank structure.

---

## 🗂️ File Naming Conventions

### Pattern
```
{topic-slug}.mdx
```

### Rules
- Use lowercase letters only
- Separate words with hyphens (kebab-case)
- Keep names descriptive but concise (max 60 characters)
- Include relevant keywords for SEO

### Examples
- `huisdierenverzekering-complete-gids-2025.mdx`
- `wachttijd-dierenverzekering.mdx`
- `gebitszorg-dierenverzekering.mdx`
- `kattenverzekering-vergelijken.mdx`

---

## 📝 Required Frontmatter Format

Every article must start with YAML frontmatter containing these exact fields:

```yaml
---
title: "Main Article Title (60-70 characters)"
metaTitle: "SEO Title for Search Results (50-60 characters)"
metaDescription: "SEO description for search results (150-160 characters)"
canonical: "https://www.zoekdierenverzekering.nl/kennisbank/{filename-without-extension}"
---
```

### Field Guidelines

**title:**
- Primary heading for the article
- Should be engaging and descriptive
- Include main keywords naturally
- 60-70 characters optimal

**metaTitle:**
- Optimized for search engine results
- Include primary keyword early
- 50-60 characters to avoid truncation
- Can differ from main title for SEO

**metaDescription:**
- Compelling summary for search results
- Include primary and secondary keywords
- 150-160 characters optimal
- Should encourage clicks

**canonical:**
- Always use the full URL format shown above
- Replace `{filename-without-extension}` with actual filename
- Ensures proper SEO canonicalization

---

## 📖 Content Structure Requirements

### 1. Main Heading (H1)
```markdown
# Article Title (matches frontmatter title)
```

### 2. Introduction Paragraph
- 2-3 sentences summarizing the article
- Include primary keyword naturally
- Set expectations for what readers will learn

### 3. Featured Image
```markdown
![Alt text description](/images/kennisbank/category/image-name.jpg)
```

### 4. Horizontal Rule
```markdown
---
```

### 5. Quick Summary Section
```markdown
## In 't kort

- **Key Point 1:** Brief explanation
- **Key Point 2:** Brief explanation  
- **Key Point 3:** Brief explanation
```

### 6. Main Content Sections
- Use H2 (##) for main sections
- Use H3 (###) for subsections
- Include relevant keywords in headings
- Keep sections focused and scannable

### 7. FAQ Section (if applicable)
```markdown
## Veelgestelde vragen (FAQ)

**Question 1?**  
Answer with relevant details.

**Question 2?**  
Answer with relevant details.
```

### 8. Summary Section
```markdown
## Samenvatting

- Key takeaway 1
- Key takeaway 2  
- Key takeaway 3
```

### 9. Internal Links Section
```markdown
### Handige links op deze site

- [/hondenverzekering](/hondenverzekering)  
- [/kattenverzekering](/kattenverzekering)  
- [/vergelijker](/vergelijker)
- [/kennisbank/related-article](/kennisbank/related-article)
```

---

## 🖼️ Image Guidelines

### Image Sourcing
1. **Search Strategy:**
   - Use Firecrawl to search for relevant, high-quality images
   - Search terms: `"{topic} Netherlands" OR "{topic} guide" OR "{topic} insurance" high quality stock photo`
   - Look for professional, clean images that match the content

2. **Image Requirements:**
   - High resolution (minimum 1200px width)
   - Professional appearance
   - Relevant to article topic
   - Copyright-free or properly licensed

### Image Placement
```
/images/kennisbank/{category}/{descriptive-name}.jpg
```

### Categories
- `verzekeren/` - General insurance topics
- `honden/` - Dog-specific content
- `katten/` - Cat-specific content
- `vergelijken/` - Comparison guides
- `kosten/` - Cost-related topics

### Alt Text
- Descriptive and specific
- Include relevant keywords naturally
- 50-100 characters optimal

---

## 📂 File Location

### Directory Structure
```
content/info/{article-name}.mdx
```

### Placement Rules
- All articles go directly in `content/info/`
- Do NOT create subdirectories
- Use the filename as the URL slug

---

## 🏷️ Category Assignment

Articles automatically appear in kennisbank categories based on:

1. **Keywords in title/content**
2. **Internal link structure**  
3. **Topic relevance**

### Main Categories
- **Algemeen** - General insurance information
- **Hondenverzekering** - Dog insurance specific
- **Kattenverzekering** - Cat insurance specific  
- **Vergelijken** - Comparison guides
- **Kosten** - Cost and pricing information

---

## ✅ Content Quality Checklist

### SEO Optimization
- [ ] Primary keyword in title, meta title, and H1
- [ ] Secondary keywords in H2 headings
- [ ] Keywords used naturally throughout content
- [ ] Meta description compelling and keyword-rich
- [ ] Internal links to relevant pages

### Content Quality
- [ ] Original, rewritten content (not copied)
- [ ] Natural, human tone
- [ ] Factually accurate information
- [ ] Proper Dutch grammar and spelling
- [ ] Scannable with bullet points and short paragraphs

### Technical Requirements
- [ ] Valid YAML frontmatter
- [ ] Proper markdown formatting
- [ ] Working internal links
- [ ] Optimized image with alt text
- [ ] Canonical URL correctly formatted

### User Experience
- [ ] Clear value proposition in introduction
- [ ] Logical content flow
- [ ] Actionable information
- [ ] Relevant FAQ section
- [ ] Helpful summary and next steps

---

## 🔄 Conversion Process

### Step 1: Content Analysis
1. Read the scraped content thoroughly
2. Identify main topic and keywords
3. Determine target audience and intent
4. Plan article structure

### Step 2: Content Rewriting
1. Rewrite in natural Dutch
2. Maintain factual accuracy
3. Optimize for target keywords
4. Ensure originality for copyright compliance

### Step 3: Structure Implementation
1. Create proper frontmatter
2. Format with required sections
3. Add internal links to relevant pages
4. Include FAQ if applicable

### Step 4: Image Integration
1. Search for relevant image using Firecrawl
2. Download and optimize image
3. Place in appropriate directory
4. Add with descriptive alt text

### Step 5: Quality Assurance
1. Review against checklist
2. Test internal links
3. Verify SEO optimization
4. Proofread for errors

### Step 6: Publication
1. Save file in `content/info/`
2. Verify article appears in kennisbank
3. Test on both desktop and mobile
4. Monitor for any issues

---

## 🚨 Common Mistakes to Avoid

1. **Copying content directly** - Always rewrite for originality
2. **Missing frontmatter fields** - All fields are required
3. **Incorrect file naming** - Use kebab-case only
4. **Wrong image paths** - Follow directory structure exactly
5. **Missing internal links** - Always link to relevant pages
6. **Poor keyword optimization** - Include keywords naturally
7. **Broken canonical URLs** - Double-check URL format
8. **Inconsistent formatting** - Follow structure exactly

---

## 📞 Support

For questions or issues with this process:
1. Review this guide thoroughly
2. Check existing articles for examples
3. Test changes on development environment first
4. Document any process improvements

---

*Last updated: August 15, 2025*
