import Link from 'next/link'

export default function Footer() {
  const footerLinks = {
    verzekeringen: [
      { name: 'Hondenverzekering', href: '/hondenverzekering' },
      { name: 'Kattenverzekering', href: '/kattenverzekering' },
      { name: 'Konijnenverzekering', href: '/konijnverzekering' },
      { name: 'Andere huisdieren', href: '/papegaaiverzekering' }
    ],
    kennisbank: [
      { name: 'Veelgestelde vragen', href: '/kennisbank' },
      { name: 'Wat is een dierenverzekering?', href: '/kennisbank' },
      { name: 'Kosten dierena<PERSON>', href: '/kennisbank' },
      { name: 'Eigen risico', href: '/kennisbank' },
      { name: 'Alle artikelen', href: '/kennisbank' }
    ],
    verzekeraars: [
      { name: 'OH<PERSON>', href: '#' },
      { name: '<PERSON><PERSON><PERSON>', href: '#' },
      { name: '<PERSON><PERSON>', href: '#' },
      { name: 'ANW<PERSON>', href: '#' },
      { name: '<PERSON><PERSON><PERSON><PERSON>hee<PERSON>', href: '#' },
      { name: 'Nationale Nederlanden', href: '#' },
      { name: 'Alle verzekeraars', href: '#' }
    ],
    overOns: [
      { name: 'Contact', href: '#' }
    ]
  }

  const bottomLinks = [
    { name: 'Privacy', href: '/privacy' },
    { name: 'Disclaimer', href: '/disclaimer' },
    { name: 'Cookies', href: '#' }
  ]

  return (
    <footer className="w-full bg-tea_green-900 relative">
      
      <div className="max-w-[1284px] mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          
          {/* Verzekeringen voor */}
          <div>
            <h3 className="text-[20px] font-bold text-celadon-100 mb-6 font-figtree leading-[1.2]">
              Verzekeringen voor
            </h3>
            <ul className="space-y-3">
              {footerLinks.verzekeringen.map((link, index) => (
                <li key={index}>
                  <Link
                    href={link.href}
                    className="text-[16px] text-celadon-100 hover:underline font-figtree leading-[1.6] transition-colors hover:text-celeste-200"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Artikelen */}
          <div>
            <h3 className="text-[20px] font-bold text-[#2F2E51] mb-6 font-figtree leading-[1.2]">
              Artikelen
            </h3>
            <ul className="space-y-3">
              {footerLinks.kennisbank.map((link, index) => (
                <li key={index}>
                  <Link
                    href={link.href}
                    className="text-[16px] text-[#2F2E51] hover:underline font-figtree leading-[1.6] transition-colors hover:text-blue-600"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Verzekeraars */}
          <div>
            <h3 className="text-[20px] font-bold text-[#2F2E51] mb-6 font-figtree leading-[1.2]">
              Verzekeraars
            </h3>
            <ul className="space-y-3">
              {footerLinks.verzekeraars.map((link, index) => (
                <li key={index}>
                  <Link
                    href={link.href}
                    className="text-[16px] text-[#2F2E51] hover:underline font-figtree leading-[1.6] transition-colors hover:text-blue-600"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Over ons */}
          <div>
            <h3 className="text-[20px] font-bold text-[#2F2E51] mb-6 font-figtree leading-[1.2]">
              Over ons
            </h3>
            <ul className="space-y-3">
              {footerLinks.overOns.map((link, index) => (
                <li key={index}>
                  <Link
                    href={link.href}
                    className="text-[16px] text-[#2F2E51] hover:underline font-figtree leading-[1.6] transition-colors hover:text-blue-600"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Bottom section */}
        <div className="border-t border-celadon-800 pt-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div className="text-[16px] text-celadon-100 font-figtree leading-[1.6]">
              © 2025 ZoekDierenverzekering.nl
            </div>
            <div className="flex gap-6">
              {bottomLinks.map((link, index) => (
                <Link
                  key={index}
                  href={link.href}
                  className="text-[16px] text-celadon-100 hover:underline font-figtree leading-[1.6] transition-colors hover:text-celeste-200"
                >
                  {link.name}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Decorative paw prints */}
      <div className="absolute bottom-8 left-8 w-[62px] h-[72px] opacity-30">
        <div className="w-full h-full bg-[#2F2E51] rounded-full"></div>
      </div>
      <div className="absolute bottom-16 left-20 w-[62px] h-[72px] opacity-30">
        <div className="w-full h-full bg-[#2F2E51] rounded-full"></div>
      </div>
    </footer>
  )
}