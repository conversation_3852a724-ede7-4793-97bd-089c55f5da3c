"use client"

import { motion } from 'framer-motion'
import { CheckCircle, DollarSign, MessageSquare, ArrowRight } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import Image from 'next/image'
import Link from 'next/link'

interface ValuePropositionProps {
  className?: string
}

export default function ModernValueProposition({ className = "" }: ValuePropositionProps) {
  const benefits = [
    {
      icon: CheckCircle,
      title: "100% Onafhankelijk Vergelijken",
      description: "Als onafhankelijk vergelijkingsplatform tonen we alle Nederlandse dierenverzekeraars zonder verborgen kosten. Vergelijk premies, dekking en voorwaarden van OHRA, PetSecur, Figo en andere top verzekeraars.",
      color: "from-cyan-500 to-blue-500"
    },
    {
      icon: DollarSign,
      title: "Goedkoopste Dierenverzekering Vinden",
      description: "Bespaar tot 30% door slim vergelijken. Onze algoritme vindt automatisch de beste prijs-kwaliteitverhouding voor jouw specifieke situatie. Kattenverzekering vanaf €10, hondenverzekering vanaf €15 per maand.",
      color: "from-green-500 to-emerald-500"
    },
    {
      icon: MessageSquare,
      title: "Expert Advies & Persoonlijke Service",
      description: "Gratis advies van gecertificeerde verzekeringsexperts. We helpen je de juiste dekking te kiezen die past bij jouw huisdier en budget. Nederlandse klantenservice, geen verkooppraatjes.",
      color: "from-purple-500 to-indigo-500"
    }
  ]

  const petTypes = [
    {
      name: "Hondenverzekering",
      description: "Vergelijk hondenverzekeringen van alle Nederlandse aanbieders. Van kleine rassen tot grote honden - vind de beste dekking voor jouw trouwe viervoeter.",
      price: "€17/maand",
      image: "/images/dog.webp",
      link: "/hondenverzekering",
      color: "from-blue-50 to-indigo-50",
      borderColor: "border-blue-100 hover:border-blue-300"
    },
    {
      name: "Kattenverzekering",
      description: "Kies uit de beste kattenverzekeringen in Nederland. Van kitten tot senior kat - bescherm je huiskat tegen hoge dierenarts kosten.",
      price: "€11/maand",
      image: "/images/cat.webp",
      link: "/kattenverzekering",
      color: "from-pink-50 to-rose-50",
      borderColor: "border-pink-100 hover:border-pink-300"
    }
  ]

  return (
    <section className={`py-16 sm:py-20 lg:py-24 bg-white ${className}`}>
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Section Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true, margin: "-100px" }}
        >
          <motion.h2
            className="text-3xl sm:text-4xl lg:text-5xl font-bold text-[#2F2E51] mb-6 font-figtree"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
            viewport={{ once: true, margin: "-100px" }}
          >
            Waarom ZoekDierenverzekering.nl de beste keuze is voor huisdier verzekeren
          </motion.h2>
          <motion.p
            className="text-lg text-gray-600 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
            viewport={{ once: true, margin: "-100px" }}
          >
            Als onafhankelijk vergelijkingsplatform helpen wij je de beste dierenverzekering te vinden 
            die perfect past bij jouw huisdier en budget.
          </motion.p>
        </motion.div>

        {/* Benefits Grid */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          {benefits.map((benefit, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 * index, ease: "easeOut" }}
              viewport={{ once: true, margin: "-50px" }}
            >
              <Card className="text-center h-full hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border-gray-200">
                <CardHeader>
                  <div className={`w-16 h-16 bg-gradient-to-br ${benefit.color} rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg`}>
                    <benefit.icon className="w-8 h-8 text-white" />
                  </div>
                  <CardTitle className="text-xl font-bold text-[#2F2E51] font-figtree">
                    {benefit.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 leading-relaxed">
                    {benefit.description}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Pet Insurance Types */}
        <motion.div
          className="mb-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
          viewport={{ once: true, margin: "-50px" }}
        >
          <h3 className="text-2xl sm:text-3xl font-bold text-[#2F2E51] text-center mb-8 font-figtree">
            Welke dierenverzekering past bij jouw huisdier?
          </h3>
          
          <div className="grid md:grid-cols-2 gap-8">
            {petTypes.map((pet, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 * index, ease: "easeOut" }}
                viewport={{ once: true, margin: "-50px" }}
              >
                <Card className={`bg-gradient-to-br ${pet.color} ${pet.borderColor} hover:shadow-lg transition-all duration-300 hover:-translate-y-1`}>
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4 mb-4">
                      <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center flex-shrink-0 shadow-md">
                        <Image
                          src={pet.image}
                          alt={pet.name}
                          width={32}
                          height={32}
                          className="w-8 h-8 object-cover rounded-full"
                        />
                      </div>
                      <div className="flex-1">
                        <h4 className="text-xl font-bold text-[#2F2E51] mb-2 font-figtree">
                          {pet.name}
                        </h4>
                        <div className="text-2xl font-bold text-green-600 mb-2">
                          Vanaf {pet.price}
                        </div>
                      </div>
                    </div>
                    <p className="text-gray-600 leading-relaxed mb-4">
                      {pet.description}
                    </p>
                    <Button asChild className="w-full bg-[#2F2E51] hover:bg-[#2F2E51]/90 text-white font-semibold">
                      <Link href={pet.link}>
                        {pet.name} Vergelijken
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}
