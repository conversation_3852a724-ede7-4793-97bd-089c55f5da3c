"use client"

import Image from 'next/image'
import Link from 'next/link'
import { motion } from 'framer-motion'

export default function PetSelectionSection() {
  return (
    <section className="w-full bg-white py-16">
      <div className="max-w-[1254px] mx-auto px-4">
        <motion.div
          className="bg-[#2F2E51] rounded-[10px] p-12 relative overflow-hidden"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true, margin: "-50px" }}
        >
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">

            {/* Title */}
            <motion.div
              className="lg:col-span-1"
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
              viewport={{ once: true, margin: "-50px" }}
            >
              <h2 className="text-[34px] font-bold text-white leading-[1.2] font-figtree">
                Welk huisdier wil je verzekeren?
              </h2>
            </motion.div>

            {/* Dog Option */}
            <motion.div
              className="lg:col-span-1"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4, ease: "easeOut" }}
              viewport={{ once: true, margin: "-50px" }}
            >
              <Link href="/hondenverzekering" className="block">
                <motion.div
                  className="bg-white border border-[#2F2E51] rounded-[10px] overflow-hidden w-full h-[123px] hover:shadow-lg transition-shadow cursor-pointer"
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="flex h-full">
                    <div className="w-1/2 h-full relative">
                      <Image
                        src="/images/dog.webp"
                        alt="Hond verzekering"
                        width={182}
                        height={121}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="w-1/2 bg-white flex items-center justify-center">
                      <span className="text-[24px] font-bold text-[#2F2E51] font-figtree">
                        Hond
                      </span>
                    </div>
                  </div>
                </motion.div>
              </Link>
            </motion.div>

            {/* Cat Option */}
            <motion.div
              className="lg:col-span-1"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6, ease: "easeOut" }}
              viewport={{ once: true, margin: "-50px" }}
            >
              <Link href="/kattenverzekering" className="block">
                <motion.div
                  className="bg-white border border-[#2F2E51] rounded-[10px] overflow-hidden w-full h-[123px] hover:shadow-lg transition-shadow cursor-pointer"
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="flex h-full">
                    <div className="w-1/2 h-full relative">
                      <Image
                        src="/images/cat.webp"
                        alt="Kat verzekering"
                        width={182}
                        height={121}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="w-1/2 bg-white flex items-center justify-center">
                      <span className="text-[24px] font-bold text-[#2F2E51] font-figtree">
                        Kat
                      </span>
                    </div>
                  </div>
                </motion.div>
              </Link>
            </motion.div>

          </div>
        </motion.div>
      </div>
    </section>
  )
}